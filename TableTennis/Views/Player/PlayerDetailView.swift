import SwiftUI
import Charts

struct PlayerDetailView: View {
    @EnvironmentObject var dataManager: DataManager
    let player: Player
    @State private var showingEditPlayer = false

    var playerMatches: [Match] {
        return dataManager.matches(for: player)
    }

    var recentMatches: [Match] {
        return playerMatches
            .filter { $0.status == .completed }
            .sorted { $0.completedAt ?? Date.distantPast > $1.completedAt ?? Date.distantPast }
            .prefix(5)
            .map { $0 }
    }

    var isTopEloPlayer: Bool {
        let sortedPlayers = dataManager.players.sorted { $0.eloRating > $1.eloRating }
        return sortedPlayers.first?.id == player.id && !dataManager.players.isEmpty
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Player Header
                PlayerHeaderView(player: player, isTopEloPlayer: isTopEloPlayer)

                // Statistics Cards
                StatisticsCardsView(player: player)

                // ELO Progress Chart (placeholder)
                EloProgressView(player: player, matches: playerMatches)

                // Recent Matches
                RecentMatchesView(player: player, matches: recentMatches)
            }
            .padding()
        }
        .overlay(
            // Confetti effect for #1 ELO player
            Group {
                if isTopEloPlayer {
                    SimpleConfettiView()
                        .allowsHitTesting(false)
                }
            }
        )
        .navigationTitle(player.name)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Edit") {
                    showingEditPlayer = true
                }
            }
        }
        .sheet(isPresented: $showingEditPlayer) {
            EditPlayerView(player: player)
        }
    }
}

struct PlayerHeaderView: View {
    let player: Player
    let isTopEloPlayer: Bool

    var body: some View {
        VStack(spacing: 12) {
            // Avatar placeholder with crown for #1 player
            ZStack {
                Circle()
                    .fill(isTopEloPlayer ?
                          Color(red: 0.95, green: 0.90, blue: 0.75) :
                          Color.blue)
                    .frame(width: 80, height: 80)
                    .overlay(
                        Text(String(player.name.prefix(1)).uppercased())
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )

                // Crown for #1 player
                if isTopEloPlayer {
                    Image(systemName: "crown.fill")
                        .font(.title2)
                        .foregroundColor(.yellow)
                        .shadow(color: .orange, radius: 2)
                        .offset(y: -45)
                }
            }

            VStack(spacing: 4) {
                HStack {
                    if isTopEloPlayer {
                        Text("👑")
                            .font(.title3)
                    }
                    Text(player.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    if isTopEloPlayer {
                        Text("👑")
                            .font(.title3)
                    }
                }

                HStack {
                    Text("ELO Rating: \(Int(player.eloRating))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    if isTopEloPlayer {
                        Text("#1")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.orange)
                            .cornerRadius(4)
                    }
                }

                Text("Member since \(player.createdAt, formatter: dateFormatter)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(isTopEloPlayer ?
                   Color.yellow.opacity(0.15) :
                   Color(.systemGray6))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isTopEloPlayer ? Color.yellow.opacity(0.6) : Color.clear, lineWidth: 2)
        )
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter
    }
}

struct StatisticsCardsView: View {
    let player: Player
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            StatCard(
                title: "Wedstrijden",
                value: "\(player.matchesWon)/\(player.matchesPlayed)",
                subtitle: "\(Int(player.matchWinPercentage))% gewonnen",
                color: .blue,
                icon: "gamecontroller"
            )
            
            StatCard(
                title: "Games",
                value: "\(player.gamesWon)/\(player.gamesPlayed)",
                subtitle: "\(Int(player.gameWinPercentage))% gewonnen",
                color: .green,
                icon: "chart.bar"
            )
            
            StatCard(
                title: "ELO Rating",
                value: "\(Int(player.eloRating))",
                subtitle: eloRankDescription(player.eloRating),
                color: .orange,
                icon: "star.fill"
            )
            
            StatCard(
                title: "Vorm",
                value: recentFormEmoji(player),
                subtitle: "Laatste 5 wedstrijden",
                color: .purple,
                icon: "chart.line.uptrend.xyaxis"
            )
        }
    }
    
    private func eloRankDescription(_ elo: Double) -> String {
        switch elo {
        case 1800...:
            return "Expert"
        case 1600..<1800:
            return "Advanced"
        case 1400..<1600:
            return "Average"
        case 1200..<1400:
            return "Beginner"
        default:
            return "Newbie"
        }
    }
    
    private func recentFormEmoji(_ player: Player) -> String {
        // Placeholder - zou gebaseerd moeten zijn op recente wedstrijden
        let winPercentage = player.matchWinPercentage
        switch winPercentage {
        case 70...:
            return "🔥"
        case 50..<70:
            return "📈"
        case 30..<50:
            return "📉"
        default:
            return "❄️"
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Spacer()
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.primary)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct EloProgressView: View {
    let player: Player
    let matches: [Match]

    private var eloHistory: [EloDataPoint] {
        // Start met de start ELO rating (1200) op de createdAt datum van de speler
        var history: [EloDataPoint] = [
            EloDataPoint(date: player.createdAt, rating: 1200.0)
        ]

        // Filter en sorteer voltooide matches waar deze speler aan heeft deelgenomen
        let playerMatches = matches
            .filter { match in
                match.status == .completed &&
                match.allPlayers.contains { $0.id == player.id } &&
                match.completedAt != nil
            }
            .sorted {
                ($0.completedAt ?? Date.distantPast) < ($1.completedAt ?? Date.distantPast)
            }

        // Simuleer ELO geschiedenis door matches opnieuw te doorlopen
        var currentRating = 1200.0

        for match in playerMatches {
            guard let completedAt = match.completedAt else { continue }

            // Gebruik opgeslagen ELO change als beschikbaar, anders bereken het
            let eloChange: Double
            if let storedChange = match.eloChanges[player.id] {
                eloChange = storedChange
            } else {
                // Fallback: bereken ELO change voor oude matches
                eloChange = 0
            }

            currentRating += eloChange
            history.append(EloDataPoint(date: completedAt, rating: currentRating))
        }

        return history
    }


    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("ELO Progress")
                .font(.headline)

            if eloHistory.count > 1 {
                Chart(eloHistory) { dataPoint in
                    LineMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("ELO Rating", dataPoint.rating)
                    )
                    .foregroundStyle(.blue)
                    .symbol(.circle)
                    .symbolSize(30)
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day)) { value in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel(format: .dateTime.month(.abbreviated).day())
                    }
                }
                .chartYAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel()
                    }
                }
                .chartYScale(domain: 1000...)

                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            } else {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
                    .frame(height: 120)
                    .overlay(
                        Text("No match history available\nPlay some matches to see your ELO progress!")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
            }
        }
    }
}

struct RecentMatchesView: View {
    let player: Player
    let matches: [Match]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Matches")
                .font(.headline)

            if matches.isEmpty {
                Text("No matches played yet")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(matches) { match in
                    PlayerMatchRowView(match: match, player: player)
                }
            }
        }
    }
}

struct PlayerMatchRowView: View {
    let match: Match
    let player: Player

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(match.description)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(match.type.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)

                if let completedAt = match.completedAt {
                    Text(completedAt, formatter: dateFormatter)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("\(match.team1GamesWon) - \(match.team2GamesWon)")
                    .font(.subheadline)
                    .fontWeight(.medium)

                // Show ELO change instead of Won/Lost
                HStack(spacing: 4) {
                    Image(systemName: eloChange >= 0 ? "arrow.up" : "arrow.down")
                        .font(.caption2)
                        .foregroundColor(eloChange >= 0 ? .green : .red)

                    Text("\(eloChange >= 0 ? "+" : "")\(Int(eloChange))")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(eloChange >= 0 ? Color.green.opacity(0.2) : Color.red.opacity(0.2))
                .foregroundColor(eloChange >= 0 ? .green : .red)
                .cornerRadius(4)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }


    private var playerWon: Bool {
        guard let winner = match.winner else { return false }

        if match.type == .singles {
            return (player.id == match.team1Player1.id && winner == .team1) ||
                   (player.id == match.team2Player1.id && winner == .team2)
        } else {
            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
            return (isTeam1 && winner == .team1) || (!isTeam1 && winner == .team2)
        }
    }

    private var eloChange: Double {
        // Gebruik opgeslagen ELO change uit de match als beschikbaar
        if let storedChange = match.eloChanges[player.id] {
            return storedChange
        }

        // Fallback: bereken ELO change (voor oude matches zonder opgeslagen changes)
        guard let winner = match.winner else { return 0.0 }

        // Bepaal of de speler heeft gewonnen
        let playerWon: Bool
        if match.type == .singles {
            playerWon = (player.id == match.team1Player1.id && winner == .team1) ||
                       (player.id == match.team2Player1.id && winner == .team2)
        } else {
            // Voor doubles en mix & match
            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
            let isTeam2 = player.id == match.team2Player1.id || player.id == match.team2Player2?.id
            playerWon = (isTeam1 && winner == .team1) || (isTeam2 && winner == .team2)
        }

        // Bereken tegenstander rating (vereenvoudigd - gebruik huidige rating)
        let opponentRating: Double
        if match.type == .singles {
            if player.id == match.team1Player1.id {
                opponentRating = match.team2Player1.eloRating
            } else {
                opponentRating = match.team1Player1.eloRating
            }
        } else {
            // Voor doubles: gebruik gemiddelde van tegenstanders
            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
            if isTeam1 {
                let team2Rating1 = match.team2Player1.eloRating
                let team2Rating2 = match.team2Player2?.eloRating ?? team2Rating1
                opponentRating = (team2Rating1 + team2Rating2) / 2.0
            } else {
                let team1Rating1 = match.team1Player1.eloRating
                let team1Rating2 = match.team1Player2?.eloRating ?? team1Rating1
                opponentRating = (team1Rating1 + team1Rating2) / 2.0
            }
        }

        // Bereken ELO wijziging (gebruik huidige rating als benadering)
        let actualScore: Double = playerWon ? 1.0 : 0.0
        let expectedScore = 1.0 / (1.0 + pow(10.0, (opponentRating - player.eloRating) / 400.0))
        return 32.0 * (actualScore - expectedScore)
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter
    }
}

// MARK: - Data Models
struct EloDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let rating: Double
}



#Preview {
    NavigationView {
        PlayerDetailView(player: Player(name: "Test Speler", competitionId: UUID()))
    }
    .environmentObject(DataManager())
}
